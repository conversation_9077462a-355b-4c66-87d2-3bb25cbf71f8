# HackX 2K25 - Landing Page

A modern, responsive hackathon landing page built with React, TypeScript, and Vite.

## 🚀 Features

- **Modern Design**: Vibrant tech-style color scheme with gradients and animations
- **Responsive**: Mobile-first design that works on all devices
- **Accessible**: ARIA labels, semantic HTML, and keyboard navigation
- **Interactive**: Smooth scrolling, hover effects, and dynamic components
- **Fast**: Built with Vite for optimal performance

## 📋 Sections

- **Hero**: Bold title, event details, and call-to-action
- **About**: Information about the hackathon and why to participate
- **Themes**: Challenge categories (HealthTech, EdTech, Climate, AI, FinTech, Smart Cities)
- **Prizes**: Prize pool breakdown and sponsor information
- **Timeline**: Detailed 48-hour event schedule
- **Judges & Mentors**: Expert panel and available mentors
- **FAQ**: Frequently asked questions with expandable answers
- **Contact**: Contact form and venue information
- **Footer**: Social links and additional resources

## 🛠️ Tech Stack

- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **CSS3** - Modern styling with custom properties
- **CSS-in-JS** - Styled JSX for component-scoped styles

## 🎨 Design Features

- **Color Scheme**: Purple, blue, cyan gradients with pink/orange accents
- **Typography**: Inter font family with JetBrains Mono for code
- **Animations**: Fade-in effects, hover transitions, and smooth scrolling
- **Layout**: CSS Grid and Flexbox for responsive layouts
- **Effects**: Backdrop blur, box shadows, and gradient overlays

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd hackx-2k25
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and visit `http://localhost:3000`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Key Components

- `Header.tsx` - Navigation with mobile menu
- `Hero.tsx` - Main landing section with CTA
- `About.tsx` - Event information and features
- `Themes.tsx` - Challenge categories
- `Prizes.tsx` - Prize information and sponsors
- `Timeline.tsx` - Event schedule
- `Judges.tsx` - Judges, mentors, and criteria
- `FAQ.tsx` - Interactive FAQ section
- `Contact.tsx` - Contact form and information
- `Footer.tsx` - Footer with links and social media

## 🎨 Customization

### Colors

Update CSS custom properties in `src/index.css`:

```css
:root {
  --primary-purple: #6366f1;
  --primary-blue: #3b82f6;
  --primary-cyan: #06b6d4;
  --accent-pink: #ec4899;
  --accent-orange: #f59e0b;
}
```

### Content

Update component content by editing the respective files in `src/components/`.

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For questions or support, please contact:
- Email: <EMAIL>
- Discord: [Join our server](#)

---

Built with ❤️ for the HackX 2K25 community
